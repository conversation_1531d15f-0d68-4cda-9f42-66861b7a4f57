import React, { useEffect } from 'react';
import { Deck } from '../types';
import FlippableCard from './FlippableCard';

interface DeckViewerModalProps {
  deck: Deck;
  onClose: () => void;
}

const DeckViewerModal: React.FC<DeckViewerModalProps> = ({ deck, onClose }) => {
  useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };
    window.addEventListener('keydown', handleEsc);
    document.body.style.overflow = 'hidden';

    return () => {
      window.removeEventListener('keydown', handleEsc);
      document.body.style.overflow = 'auto';
    };
  }, [onClose]);

  return (
    <div
      className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4 animate-fade-in"
      onClick={onClose}
    >
      <div
        className="bg-neutral-900/80 border border-neutral-700 rounded-2xl w-full max-w-6xl h-[90vh] p-6 relative flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center mb-4 flex-shrink-0">
          <h2 className="text-3xl font-cinzel font-bold text-yellow-400">{deck.name}</h2>
          <button
            onClick={onClose}
            className="w-10 h-10 rounded-full bg-neutral-800 hover:bg-red-500/20 text-neutral-400 hover:text-white flex items-center justify-center transition-all"
            aria-label="Close deck viewer"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
          </button>
        </div>
        <div className="overflow-y-auto flex-grow pr-2">
           <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6">
                {deck.cards.map((card, index) => (
                    <div key={index} className="flex justify-center">
                       <FlippableCard data={card} />
                    </div>
                ))}
            </div>
        </div>
      </div>
    </div>
  );
};

export default DeckViewerModal;