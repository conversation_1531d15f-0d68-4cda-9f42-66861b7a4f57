# Bible Card Generator

A React application that generates Bible-themed collectible cards using Mistral AI, with intelligent fallbacks for images and content.

## Features

- **AI-Powered Card Generation**: Uses Mistral AI to create unique Bible-themed cards
- **Intelligent Fallbacks**: 
  - Images: Falls back to Picsum Photos for random images
  - Bible Content: Falls back to BibleHub for relevant verses
  - Complete Fallback Mode: Works without API keys using curated content
- **Card Collection**: Save and organize cards into themed decks
- **Interactive UI**: Flip cards, view deck collections, and generate new cards

## Run Locally

**Prerequisites:** Node.js

1. Install dependencies:
   ```bash
   npm install
   ```

2. Set up your environment:
   - Create a `.env.local` file in the root directory
   - Add your Mistral API key: `VITE_MISTRAL_API_KEY=your_api_key_here`
   - Get your API key from https://console.mistral.ai/
   - Note: The app will work in fallback mode without an API key

3. Run the app:
   ```bash
   npm run dev
   ```

## Fallback System

The application includes a robust fallback system:

- **No API Key**: Uses curated Bible verses and Picsum images
- **API Errors**: Automatically switches to fallback mode
- **Network Issues**: Gracefully handles connection problems
- **Content Filtering**: Falls back when AI content is blocked

## Card Structure

Each card includes:
- Name and Bible verse
- Attack/Defense stats
- Suit (Cross, Stone Tablet, Dove, Olive Branch)
- Rank (A, K, Q, J, 10-2, Joker)
- Deck theme
- Generated artwork
