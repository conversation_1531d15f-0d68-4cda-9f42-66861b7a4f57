
import React from 'react';
import { SparklesIcon } from './icons/SparklesIcon';

interface SearchBarProps {
  prompt: string;
  setPrompt: (prompt: string) => void;
  onGenerate: () => void;
  isLoading: boolean;
}

const SearchBar: React.FC<SearchBarProps> = ({ prompt, setPrompt, onGenerate, isLoading }) => {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!isLoading) {
        onGenerate();
      }
    }
  };
    
  return (
    <div className="flex flex-col sm:flex-row items-center gap-4 w-full">
      <textarea
        value={prompt}
        onChange={(e) => setPrompt(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder="e.g., 'An archangel wielding a flaming sword of truth'"
        className="w-full text-base bg-neutral-800 border-2 border-neutral-700 rounded-lg px-4 py-3 placeholder-neutral-500 focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 focus:outline-none transition duration-300 resize-none"
        rows={2}
        disabled={isLoading}
      />
      <button
        onClick={onGenerate}
        disabled={isLoading || !prompt.trim()}
        className="w-full sm:w-auto flex-shrink-0 flex items-center justify-center gap-2 bg-gradient-to-r from-yellow-500 to-amber-600 text-neutral-900 font-bold px-6 py-3 rounded-lg shadow-lg hover:shadow-yellow-500/40 transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:scale-100 disabled:shadow-none"
      >
        <SparklesIcon className="w-5 h-5" />
        <span>{isLoading ? 'Summoning...' : 'Generate'}</span>
      </button>
    </div>
  );
};

export default SearchBar;
