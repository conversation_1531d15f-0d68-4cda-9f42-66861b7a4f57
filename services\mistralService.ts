import { CardData } from '../types';

// Check for API key and provide helpful error message
const apiKey = import.meta.env.VITE_MISTRAL_API_KEY || process.env.MISTRAL_API_KEY || process.env.API_KEY;
if (!apiKey) {
    console.warn("MISTRAL_API_KEY environment variable not set. Using fallback mode.");
}

const DECK_THEMES = ["Covenant Cards", "The Genesis Deck", "Faith & Promise", "The Story Deck", "Parables & Promises"];

// Unified AI service using Mistral API with fallbacks
class MistralService {
    private apiKey: string;
    private baseUrl: string = 'https://api.mistral.ai/v1';

    constructor(apiKey: string) {
        this.apiKey = apiKey;
    }

    private async makeRequest(endpoint: string, payload: any): Promise<any> {
        try {
            if (!this.apiKey) {
                throw new Error('API_KEY_MISSING');
            }

            const response = await fetch(`${this.baseUrl}${endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`,
                },
                body: JSON.stringify(payload),
            });

            if (!response.ok) {
                if (response.status === 402) {
                    throw new Error('PAYMENT_REQUIRED');
                }
                if (response.status === 401) {
                    throw new Error('INVALID_API_KEY');
                }
                throw new Error(`Mistral API error: ${response.status} ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Mistral API request failed:', error);
            throw error;
        }
    }

    private async fetchBibleVerseFallback(concept: string): Promise<string> {
        try {
            // Try to get a relevant verse from BibleHub
            const searchTerms = concept.split(' ').slice(0, 3).join(' ');
            const response = await fetch(`https://biblehub.com/search.php?q=${encodeURIComponent(searchTerms)}`);
            
            if (response.ok) {
                // For now, return a default verse since we can't easily parse BibleHub's HTML
                // In a real implementation, you'd want to parse the search results
                return "For I know the plans I have for you, declares the Lord, plans to prosper you and not to harm you, plans to give you hope and a future. (Jeremiah 29:11)";
            }
        } catch (error) {
            console.warn('BibleHub fallback failed:', error);
        }
        
        // Ultimate fallback verses
        const fallbackVerses = [
            "For God so loved the world that he gave his one and only Son. (John 3:16)",
            "I can do all things through Christ who strengthens me. (Philippians 4:13)",
            "The Lord is my shepherd, I shall not want. (Psalm 23:1)",
            "Trust in the Lord with all your heart. (Proverbs 3:5)",
            "Be strong and courageous. Do not be afraid. (Joshua 1:9)"
        ];
        
        return fallbackVerses[Math.floor(Math.random() * fallbackVerses.length)];
    }

    private async fetchImageFallback(description: string): Promise<string> {
        try {
            // Use Picsum for random images as fallback
            const width = 400;
            const height = 300;
            const randomSeed = Math.floor(Math.random() * 1000);
            return `https://picsum.photos/seed/${randomSeed}/${width}/${height}`;
        } catch (error) {
            console.warn('Picsum fallback failed:', error);
            // Return a data URL as ultimate fallback
            return this.generatePlaceholderArtwork(description);
        }
    }

    private createFallbackCard(prompt: string): Promise<CardData> {
        return this.createFallbackCardWithFallbacks(prompt);
    }

    private async createFallbackCardWithFallbacks(prompt: string): Promise<CardData> {
        // Create a fallback card when API is unavailable
        const suits = [
            { suit: 'Cross', suitName: 'Love' },
            { suit: 'Stone Tablet', suitName: 'Covenant' },
            { suit: 'Dove', suitName: 'Spirit' },
            { suit: 'Olive Branch', suitName: 'Peace' },
        ];
        const ranks = ['A', 'K', 'Q', 'J', '10', '9', '8', '7', '6', '5', '4', '3', '2'];
        
        const randomSuit = suits[Math.floor(Math.random() * suits.length)];
        const randomRank = ranks[Math.floor(Math.random() * ranks.length)];
        const randomDeck = DECK_THEMES[Math.floor(Math.random() * DECK_THEMES.length)];
        
        // Use fallbacks for verse and image
        const verse = await this.fetchBibleVerseFallback(prompt);
        const imageUrl = await this.fetchImageFallback(`Fallback card for: ${prompt}`);
        
        return {
            name: prompt.length > 20 ? prompt.substring(0, 20) + "..." : prompt,
            verse: verse,
            attack: Math.floor(Math.random() * 15) + 1,
            defense: Math.floor(Math.random() * 15) + 1,
            suit: randomSuit.suit,
            suitName: randomSuit.suitName,
            rank: randomRank,
            deckName: randomDeck,
            imageUrl: imageUrl
        };
    }

    async generateCardData(prompt: string): Promise<CardData> {
        // If no API key, return fallback card immediately
        if (!this.apiKey) {
            console.warn('No API key available, using fallback card generation');
            return this.createFallbackCard(prompt);
        }

        const systemPrompt = `You are an expert theologian and card game designer. Create a Bible-themed collectible card based on the user's concept.

Return a JSON object with the following structure:
{
  "name": "A biblical and powerful name (max 3 words)",
  "verse": "A relevant Bible verse with citation (e.g., 'John 3:16')",
  "attack": "Power/influence number 0-20 (0 if not a character)",
  "defense": "Resilience/faith number 0-20 (0 if not a character)",
  "suit": "One of: Cross, Stone Tablet, Dove, Olive Branch",
  "suitName": "One of: Love, Covenant, Spirit, Peace",
  "rank": "One of: A, K, Q, J, 10, 9, 8, 7, 6, 5, 4, 3, 2, Joker",
  "deckName": "One of: ${DECK_THEMES.join(', ')}",
  "imageDescription": "Detailed description for artwork in classical religious art style"
}

Guidelines:
- Suits: Cross=Love, Stone Tablet=Covenant, Dove=Spirit, Olive Branch=Peace
- Ranks should reflect importance/power level
- Image descriptions should be detailed but avoid text/symbols
- Choose appropriate deck theme based on content`;

        const payload = {
            model: 'mistral-large-latest',
            messages: [
                { role: 'system', content: systemPrompt },
                { role: 'user', content: `Create a Bible-themed card for: ${prompt}` }
            ],
            temperature: 0.7,
            max_tokens: 1000,
            response_format: { type: 'json_object' }
        };

        try {
            const response = await this.makeRequest('/chat/completions', payload);
            const content = response.choices[0]?.message?.content;
            
            if (!content) {
                throw new Error('No content received from Mistral API');
            }

            const cardData = JSON.parse(content);
            
            // Try to get an image from Picsum as fallback
            const imageUrl = await this.fetchImageFallback(cardData.imageDescription || cardData.name);

            return {
                name: cardData.name,
                verse: cardData.verse,
                attack: parseInt(cardData.attack) || 0,
                defense: parseInt(cardData.defense) || 0,
                suit: cardData.suit,
                suitName: cardData.suitName,
                rank: cardData.rank,
                deckName: cardData.deckName,
                imageUrl: imageUrl
            };

        } catch (error) {
            console.error('Failed to generate card data:', error);
            
            // Handle specific API errors with helpful messages and fallbacks
            if (error.message === 'PAYMENT_REQUIRED') {
                console.warn('Mistral API quota exceeded, using fallback card generation');
                return this.createFallbackCard(prompt);
            }
            
            if (error.message === 'INVALID_API_KEY') {
                console.warn('Invalid Mistral API key, using fallback card generation');
                return this.createFallbackCard(prompt);
            }
            
            if (error.message === 'API_KEY_MISSING') {
                console.warn('Mistral API key missing, using fallback card generation');
                return this.createFallbackCard(prompt);
            }
            
            // For any other errors, also use fallback
            console.warn('Mistral API error, using fallback card generation:', error.message);
            return this.createFallbackCard(prompt);
        }
    }

    private generatePlaceholderArtwork(description: string): string {
        // Create a sophisticated SVG placeholder based on the description
        const colors = {
            'Cross': '#dc2626', // red
            'Stone Tablet': '#7c2d12', // brown
            'Dove': '#1e40af', // blue
            'Olive Branch': '#166534' // green
        };

        const defaultColor = '#facc15'; // gold
        
        return "data:image/svg+xml;base64," + btoa(`
            <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#1a1a1a;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#0a0a0a;stop-opacity:1" />
                    </linearGradient>
                    <filter id="glow">
                        <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                        <feMerge> 
                            <feMergeNode in="coloredBlur"/>
                            <feMergeNode in="SourceGraphic"/>
                        </feMerge>
                    </filter>
                </defs>
                <rect width="100%" height="100%" fill="url(#bg)"/>
                <circle cx="200" cy="150" r="60" fill="${defaultColor}" opacity="0.2" filter="url(#glow)"/>
                <text x="50%" y="40%" dominant-baseline="middle" text-anchor="middle" fill="${defaultColor}" font-family="serif" font-size="18" font-weight="bold">Sacred Artwork</text>
                <text x="50%" y="55%" dominant-baseline="middle" text-anchor="middle" fill="#a3a3a3" font-family="serif" font-size="12">${description.substring(0, 40)}...</text>
                <text x="50%" y="75%" dominant-baseline="middle" text-anchor="middle" fill="#737373" font-family="serif" font-size="10">Mistral AI Generated</text>
            </svg>
        `);
    }
}

// Create singleton instance
const mistralService = new MistralService(apiKey);

export const generateCardData = (prompt: string): Promise<CardData> => {
    return mistralService.generateCardData(prompt);
}; 