import React, { useState } from 'react';
import { CardData } from '../types';
import Card from './Card';
import CardBack from './CardBack';

interface FlippableCardProps {
  data: CardData;
}

const FlippableCard: React.FC<FlippableCardProps> = ({ data }) => {
  const [isFlipped, setIsFlipped] = useState(false);

  const handleFlip = (e: React.MouseEvent) => {
    // Prevent clicks from bubbling up, e.g., closing a modal
    e.stopPropagation();
    setIsFlipped(!isFlipped);
  };

  return (
    <div className="flip-card w-80 h-[28rem]" onClick={handleFlip}>
      <div className={`flip-card-inner ${isFlipped ? 'is-flipped' : ''}`}>
        <div className="flip-card-front">
          <CardBack />
        </div>
        <div className="flip-card-back">
          <Card data={data} />
        </div>
      </div>
    </div>
  );
};

export default FlippableCard;
