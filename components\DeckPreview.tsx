import React from 'react';
import { Deck } from '../types';
import { SparklesIcon } from './icons/SparklesIcon';

interface DeckPreviewProps {
  deck: Deck;
  onClick: () => void;
}

const DeckPreview: React.FC<DeckPreviewProps> = ({ deck, onClick }) => {
  return (
    <div
      onClick={onClick}
      className="cursor-pointer group aspect-[3/4] bg-neutral-800 rounded-lg p-4 flex flex-col justify-center items-center text-center border-2 border-neutral-700 hover:border-yellow-500 hover:shadow-xl hover:shadow-yellow-500/20 transform hover:-translate-y-1 transition-all duration-300"
    >
      <SparklesIcon className="w-12 h-12 text-neutral-500 group-hover:text-yellow-400 transition-colors" />
      <h3 className="mt-4 font-cinzel text-xl font-bold text-neutral-300 group-hover:text-white transition-colors">{deck.name}</h3>
      <p className="text-sm text-neutral-400">{deck.cards.length} Cards</p>
    </div>
  );
};

export default DeckPreview;
