
import React, { useState, useCallback, useEffect } from 'react';
import { CardData, Deck } from './types';
import { generateCardData } from './services/mistralService';
import SearchBar from './components/SearchBar';
import Loader from './components/Loader';
import { SparklesIcon } from './components/icons/SparklesIcon';
import DeckPreview from './components/DeckPreview';
import DeckViewerModal from './components/DeckViewerModal';
import FlippableCard from './components/FlippableCard';
import CardBack from './components/CardBack';

declare module 'react';
declare module 'react/jsx-runtime';

const STORAGE_KEY = 'bible-card-collection';
const DECK_SIZE = 54;

// Helper function to create mock data for testing
const createTestDeck = (): Deck => {
    const ranks = ['A', 'K', 'Q', 'J', '10', '9', '8', '7', '6', '5', '4', '3', '2'];
    const suits = [
        { suit: 'Cross', suitName: 'Love' },
        { suit: 'Stone Tablet', suitName: 'Covenant' },
        { suit: 'Dove', suitName: 'Spirit' },
        { suit: 'Olive Branch', suitName: 'Peace' },
    ];
    const cards: CardData[] = [];
    let cardCount = 0;

    for (const rank of ranks) {
        for (const suitInfo of suits) {
            cards.push({
                name: `Test ${suitInfo.suitName} ${rank}`,
                verse: `A test verse for the ${rank} of ${suitInfo.suitName}. (Genesis 1:${cardCount + 1})`,
                attack: Math.floor(Math.random() * 15) + 1,
                defense: Math.floor(Math.random() * 15) + 1,
                // imageUrl is intentionally omitted to simulate a stored card
                suit: suitInfo.suit,
                suitName: suitInfo.suitName,
                rank: rank,
                deckName: 'The Genesis Deck',
            });
        }
    }
    
    // Add 2 Jokers
    for (let i = 0; i < 2; i++) {
        cards.push({
            name: `Thematic Joker ${i + 1}`,
            verse: `A test verse for a Joker. (Revelation ${i+1}:${i+1})`,
            attack: 20,
            defense: 20,
            // imageUrl is intentionally omitted
            suit: 'Dove',
            suitName: 'Spirit',
            rank: 'Joker',
            deckName: 'The Genesis Deck',
        });
    }

    return { name: 'The Genesis Deck (Test)', cards };
};


const App: React.FC = () => {
  const [prompt, setPrompt] = useState<string>('');
  const [cardData, setCardData] = useState<CardData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [completedDecks, setCompletedDecks] = useState<Deck[]>([]);
  const [selectedDeck, setSelectedDeck] = useState<Deck | null>(null);

useEffect(() => {
    // Load decks from local storage on initial render
    try {
      const allCards: CardData[] = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');
      const decksMap: { [key: string]: CardData[] } = {};

      allCards.forEach(card => {
        if (!decksMap[card.deckName]) {
          decksMap[card.deckName] = [];
        }
        decksMap[card.deckName].push(card);
      });

      const decks = Object.entries(decksMap)
        .filter(([, cards]) => cards.length >= DECK_SIZE)
        .map(([name, cards]) => ({ name, cards: cards.slice(0, DECK_SIZE) }));

      // FOR TESTING: If no real decks are completed, show a test deck.
      if (decks.length === 0) {
        console.log("TESTING: No completed decks found. Loading a sample test deck.");
        decks.push(createTestDeck());
      }

      setCompletedDecks(decks);

    } catch (e) {
      console.error("Failed to load decks from local storage", e);
      localStorage.removeItem(STORAGE_KEY);
      setCompletedDecks([]);
    }
  }, []);
  
const processNewCard = (newCard: CardData) => {
    try {
        const allCards: CardData[] = JSON.parse(localStorage.getItem(STORAGE_KEY) || '[]');

        // THE FIX: Create a version of the card without the large image data for storage.
        const { imageUrl, ...cardToStore } = newCard;

        allCards.push(cardToStore);
        localStorage.setItem(STORAGE_KEY, JSON.stringify(allCards));

        // Check if this new card completes a deck
        const deckCards = allCards.filter(card => card.deckName === newCard.deckName);
        if (deckCards.length >= DECK_SIZE) {
            const newCompletedDeck: Deck = { name: newCard.deckName, cards: deckCards.slice(0, DECK_SIZE) };
            setCompletedDecks(prevDecks => {
                // Filter out the test deck if it exists and add the new real one
                const realDecks = prevDecks.filter(deck => !deck.name.includes('(Test)'));
                // Avoid adding duplicate real decks
                if (!realDecks.some(deck => deck.name === newCompletedDeck.name)) {
                   return [...realDecks, newCompletedDeck];
                }
                return realDecks;
            });
        }
    } catch(e) {
        console.error("Failed to save card to local storage", e);
        if (e instanceof DOMException && e.name === 'QuotaExceededError') {
             setError("Storage is full. Cannot save new card. Please clear site data to continue saving.");
        } else {
            setError("An unexpected error occurred. Please try again.");
        }
    }
};

  const handleGenerate = useCallback(async () => {
    if (!prompt.trim()) {
      setError('Please enter a concept for your card.');
      return;
    }
    setError(null);
    setIsLoading(true);
    setCardData(null);

    try {
      const data = await generateCardData(prompt);
      if (data) {
        setCardData(data);
        processNewCard(data);
      } else {
        setError('Failed to generate the card. The request may have been blocked by the content filter, or the ancient scrolls may be sealed. Please adjust your prompt and try again.');
      }
    } catch (err) {
      console.error(err);
      setError('Failed to generate the card. The request may have been blocked by the content filter, or the ancient scrolls may be sealed. Please adjust your prompt and try again.');
    } finally {
      setIsLoading(false);
    }
  }, [prompt]);

  return (
    <div className="min-h-screen bg-gradient-to-b from-neutral-900 to-black flex flex-col items-center p-4 sm:p-6 md:p-8">
      <main className="w-full max-w-5xl mx-auto flex flex-col items-center text-center">
        <header className="mb-8">
           <div className="flex justify-center items-center gap-3 mb-2">
             <SparklesIcon className="w-10 h-10 text-yellow-400" />
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold font-cinzel tracking-wider text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 via-yellow-500 to-amber-600">
              Bible Card Generator
            </h1>
           </div>
          <p className="text-neutral-400 max-w-2xl mx-auto">
            Forge a new legend. Describe a character, story, or concept from the scriptures, and the divine scribes shall bring it to life as a card.
          </p>
        </header>

        <div className="w-full max-w-lg mb-8">
          <SearchBar
            prompt={prompt}
            setPrompt={setPrompt}
            onGenerate={handleGenerate}
            isLoading={isLoading}
          />
        </div>
        
        {error && <div className="mt-4 text-red-400 bg-red-900/50 p-3 rounded-lg border border-red-700">{error}</div>}

        <div className="w-full flex justify-center items-start mt-8 min-h-[480px]">
          {isLoading && <Loader />}
          {!isLoading && cardData && (
            <div className="animate-fade-in">
              <FlippableCard data={cardData} />
            </div>
          )}
          {!isLoading && !cardData && (
             <div className="w-80 h-[28rem] animate-fade-in">
                <CardBack />
            </div>
          )}
        </div>
      </main>

      {completedDecks.length > 0 && (
        <section className="w-full max-w-5xl mx-auto mt-16">
          <h2 className="text-3xl font-cinzel font-bold text-center mb-8 text-yellow-400">Completed Decks</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {completedDecks.map(deck => (
              <DeckPreview key={deck.name} deck={deck} onClick={() => setSelectedDeck(deck)} />
            ))}
          </div>
        </section>
      )}

      {selectedDeck && (
        <DeckViewerModal deck={selectedDeck} onClose={() => setSelectedDeck(null)} />
      )}
    </div>
  );
};

export default App;
