import React from 'react';
import { SparklesIcon } from './icons/SparklesIcon';

const CardBack: React.FC = () => {
    // A premium, non-revealing card back design inspired by the 'Outlaws' box
    return (
        <div className="w-full h-full bg-neutral-800 rounded-2xl border-2 border-neutral-600 shadow-lg p-2 flex items-center justify-center relative overflow-hidden">
            {/* Ornate border simulation */}
            <div className="absolute inset-0 m-1 border border-yellow-800/40 rounded-xl"></div>
            <div className="absolute inset-0 m-2 border-2 border-neutral-900 rounded-lg"></div>
            <div className="absolute inset-0 m-3 border border-yellow-800/60 rounded-md"></div>

            <div className="w-full h-full border border-neutral-700 rounded-lg flex flex-col items-center justify-center p-4">
                <SparklesIcon className="w-16 h-16 text-yellow-600/70" />
                <div className="w-3/4 h-px bg-gradient-to-r from-transparent via-yellow-700 to-transparent my-4"></div>
                <h3 className="font-cinzel text-2xl text-transparent bg-clip-text bg-gradient-to-b from-neutral-300 to-neutral-500 tracking-widest">
                    BIBLE
                </h3>
                <h4 className="font-cinzel text-md text-neutral-500 tracking-wider">
                    DECK
                </h4>
                <div className="w-3/4 h-px bg-gradient-to-r from-transparent via-yellow-700 to-transparent my-4"></div>
                <SparklesIcon className="w-16 h-16 text-yellow-600/70 transform rotate-180" />
            </div>
        </div>
    );
};

export default CardBack;
