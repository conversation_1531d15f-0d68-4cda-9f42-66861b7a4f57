
import React from 'react';
import { CardData } from '../types';
import { SwordIcon } from './icons/SwordIcon';
import { ShieldIcon } from './icons/ShieldIcon';
import { CrossIcon } from './icons/CrossIcon';
import { StoneTabletIcon } from './icons/StoneTabletIcon';
import { DoveIcon } from './icons/DoveIcon';
import { OliveBranchIcon } from './icons/OliveBranchIcon';


interface CardProps {
  data: CardData;
}

const suitIcons: { [key: string]: React.FC<React.SVGProps<SVGSVGElement>> } = {
  'Cross': CrossIcon,
  'Stone Tablet': StoneTabletIcon,
  'Dove': DoveIcon,
  'Olive Branch': OliveBranchIcon,
};

const getSuitColor = (suit: string): string => {
  switch (suit) {
    case 'Cross':
    case 'Stone Tablet':
      return 'text-red-400'; // Red suits
    case 'Dove':
    case 'Olive Branch':
    default:
      return 'text-neutral-100'; // "Black" suits (white on dark theme)
  }
};

const Card: React.FC<CardProps> = ({ data }) => {
  const isCreature = data.attack > 0 || data.defense > 0;
  const SuitIconComponent = suitIcons[data.suit];
  const suitColor = getSuitColor(data.suit);


  return (
    <div className="relative w-80 h-[28rem] bg-neutral-900 rounded-2xl border-2 border-neutral-700 shadow-2xl shadow-yellow-500/10 p-2.5 flex flex-col gap-2.5">
      {/* Rank Display */}
      {data.rank && (
        <div title={`Rank: ${data.rank}`} className="absolute top-4 left-4 w-10 h-10 bg-neutral-800/70 backdrop-blur-sm border border-neutral-600 rounded-full flex items-center justify-center z-10">
            <span className={`font-cinzel font-bold text-xl ${suitColor}`}>{data.rank}</span>
        </div>
      )}

      {/* Image Container */}
      <div className="w-full h-64 rounded-lg overflow-hidden border border-neutral-600 bg-neutral-950 flex items-center justify-center text-center p-4">
        {data.imageUrl ? (
            <img src={data.imageUrl} alt={data.name} className="w-full h-full object-cover" />
        ) : (
            <div className="text-neutral-500">
                <p className="font-semibold font-cinzel">Artwork Not Saved</p>
                <p className="text-xs mt-1">Image is not stored in the collection to save space.</p>
            </div>
        )}
      </div>

      {/* Suit Icon */}
      {SuitIconComponent && (
        <div title={`Suit: ${data.suitName}`} className="absolute top-4 right-4 w-10 h-10 bg-neutral-800/70 backdrop-blur-sm border border-neutral-600 rounded-full flex items-center justify-center z-10">
            <SuitIconComponent className={`w-6 h-6 ${suitColor}`} />
        </div>
      )}


      {/* Title & Type */}
      <div className="px-2 mt-1">
        <h2 className="text-2xl font-cinzel font-bold text-neutral-100 tracking-wide truncate">{data.name}</h2>
        <div className="h-px bg-gradient-to-r from-yellow-700 via-yellow-500 to-yellow-700 my-1"></div>
      </div>
      
      {/* Verse Box */}
      <div className="bg-black/30 border border-neutral-700 rounded-md p-3 mx-1.5 text-neutral-200 flex-grow flex items-center justify-center">
        <p className="font-uncial text-base italic leading-relaxed text-center">"{data.verse}"</p>
      </div>

      {/* Stats */}
      {isCreature && (
        <div className="self-end px-2 pb-1 pt-1">
            <div className="bg-gradient-to-br from-neutral-800 to-neutral-900 border border-neutral-600 rounded-full px-4 py-2 flex items-center gap-4 shadow-lg">
                <div className="flex items-center gap-1.5 text-red-400">
                    <SwordIcon className="w-5 h-5"/>
                    <span className="text-lg font-bold font-cinzel">{data.attack}</span>
                </div>
                <div className="w-px h-6 bg-neutral-600"></div>
                <div className="flex items-center gap-1.5 text-blue-400">
                    <ShieldIcon className="w-5 h-5"/>
                    <span className="text-lg font-bold font-cinzel">{data.defense}</span>
                </div>
            </div>
        </div>
      )}
    </div>
  );
};

export default Card;
